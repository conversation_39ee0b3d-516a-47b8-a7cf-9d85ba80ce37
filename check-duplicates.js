// Script para detectar marcas duplicadas
const fs = require('fs');
const path = require('path');

console.log('🔍 DETECTANDO MARCAS DUPLICADAS\n');

// Leer el archivo de marcas
const brandsDataPath = path.join(__dirname, 'constants/reference-data/brands-data.ts');
const content = fs.readFileSync(brandsDataPath, 'utf8');

// Extraer todos los nombres de marcas
const nameMatches = content.match(/name:\s*'([^']+)'/g);
const names = nameMatches ? nameMatches.map(match => match.match(/name:\s*'([^']+)'/)[1]) : [];

// Detectar duplicados
const nameCount = {};
const duplicates = [];

names.forEach(name => {
  nameCount[name] = (nameCount[name] || 0) + 1;
  if (nameCount[name] === 2) {
    duplicates.push(name);
  }
});

console.log('📊 RESUMEN:');
console.log(`   Total nombres encontrados: ${names.length}`);
console.log(`   Nombres únicos: ${Object.keys(nameCount).length}`);
console.log(`   Duplicados encontrados: ${duplicates.length}`);

if (duplicates.length > 0) {
  console.log('\n🚨 MARCAS DUPLICADAS:');
  duplicates.forEach(name => {
    console.log(`   ❌ "${name}" aparece ${nameCount[name]} veces`);
  });
} else {
  console.log('\n✅ No se encontraron duplicados');
}

// Extraer todos los IDs de marcas
const idMatches = content.match(/{\s*id:\s*'([^']+)'/g);
const ids = idMatches ? idMatches.map(match => match.match(/id:\s*'([^']+)'/)[1]) : [];

// Detectar IDs duplicados
const idCount = {};
const duplicateIds = [];

ids.forEach(id => {
  idCount[id] = (idCount[id] || 0) + 1;
  if (idCount[id] === 2) {
    duplicateIds.push(id);
  }
});

console.log('\n📋 IDs:');
console.log(`   Total IDs encontrados: ${ids.length}`);
console.log(`   IDs únicos: ${Object.keys(idCount).length}`);
console.log(`   IDs duplicados: ${duplicateIds.length}`);

if (duplicateIds.length > 0) {
  console.log('\n🚨 IDs DUPLICADOS:');
  duplicateIds.forEach(id => {
    console.log(`   ❌ ID "${id}" aparece ${idCount[id]} veces`);
  });
} else {
  console.log('\n✅ No se encontraron IDs duplicados');
}

console.log('\n✅ VERIFICACIÓN COMPLETADA');
