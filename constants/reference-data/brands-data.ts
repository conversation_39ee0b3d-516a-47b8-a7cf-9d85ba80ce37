export interface ProductLine {
  id: string;
  name: string;
  description?: string;
  category?: 'hair-color' | 'treatment' | 'styling' | 'bleaching' | 'developer' | 'other';
  isColorLine?: boolean; // Helper para filtrado rápido
}

export interface Brand {
  id: string;
  name: string;
  country: string;
  description?: string;
  lines: ProductLine[];
}

export const professionalHairColorBrands: Brand[] = [
  // German Brands
  {
    id: 'wella',
    name: 'Wella Professionals',
    country: 'Germany',
    description: 'Leading professional hair color brand',
    lines: [
      {
        id: 'illumina',
        name: 'Illumina Color',
        description: 'Microlight technology for natural-looking color with luminous shine',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'koleston',
        name: 'Koleston Perfect',
        description: 'Permanent color with ME+ technology for reduced allergic reactions',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'color-touch',
        name: 'Color Touch',
        description: 'Demi-permanent color with up to 70% coverage and damage-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'blondor',
        name: '<PERSON>lon<PERSON>',
        description: 'Professional lightening system up to 9 levels with anti-yellow molecules',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'welloxon',
        name: 'Welloxon Perfect',
        description: 'Creamy developer in 6%, 9%, 12% for optimal color results',
        category: 'developer',
        isColorLine: true,
      },
      {
        id: 'color-fresh',
        name: 'Color Fresh Create',
        description: 'Semi-permanent fashion colors lasting up to 20 washes',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'magma',
        name: 'Magma',
        description: 'Pigmented lightener for highlights without pre-lightening',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'shinefinity',
        name: 'Shinefinity',
        description: 'Zero lift, zero damage glazing service with balanced pH',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'schwarzkopf',
    name: 'Schwarzkopf Professional',
    country: 'Germany',
    description: 'German engineering for hair color',
    lines: [
      {
        id: 'igora-royal',
        name: 'Igora Royal',
        description: 'High-performance permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'blondme',
        name: 'BlondMe',
        description: 'Premium lightening system',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'color10',
        name: 'Color10',
        description: '10-minute color service',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'igora-vibrance',
        name: 'Igora Vibrance',
        description: 'Demi-permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'igora-expert',
        name: 'Igora Expert Mousse',
        description: 'Toning mousse',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'fibreplex',
        name: 'Fibreplex',
        description: 'Bond connector technology',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'igora-zero',
        name: 'Igora Zero AMM',
        description: 'Ammonia-free color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'igora-color-worx',
        name: 'Igora ColorWorx',
        description: 'Direct dye color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'goldwell',
    name: 'Goldwell',
    country: 'Germany',
    description: 'German precision in color',
    lines: [
      {
        id: 'topchic',
        name: 'Topchic',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'colorance',
        name: 'Colorance',
        description: 'Demi-permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'silk-lift',
        name: 'Silk Lift',
        description: 'Gentle lightening',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'elumen',
        name: 'Elumen',
        description: 'High-performance color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'nectaya',
        name: 'Nectaya',
        description: 'Ammonia-free color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'oxycur',
        name: 'Oxycur Platin',
        description: 'Lightening powder',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'colorance-ph68',
        name: 'Colorance pH 6.8',
        description: 'Acid color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'kadus',
    name: 'Kadus Professional',
    country: 'Germany',
    description: 'Creative color solutions',
    lines: [
      {
        id: 'kadus-color',
        name: 'Kadus Color',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'kadus-fervidol',
        name: 'Fervidol',
        description: 'Brilliant color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'kadus-visible',
        name: 'Visible Repair',
        description: 'Reconstructive color',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  // French Brands
  {
    id: 'loreal',
    name: "L'Oréal Professionnel",
    country: 'France',
    description: 'Professional hair color innovation',
    lines: [
      {
        id: 'majirel',
        name: 'Majirel',
        description: 'Permanent color with Ionène G + Incell for 100% coverage and care',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'inoa',
        name: 'INOA',
        description: 'Oil Delivery System ammonia-free color with 60% oils',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'dia-light',
        name: 'Dia Light',
        description: 'Acidic gel-crème color pH 6.3 for shine and tone',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'dia-richesse',
        name: 'Dia Richesse',
        description: 'Alkaline demi-permanent for 70% white hair coverage',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'majirouge',
        name: 'Majirouge',
        description: 'Carmilane micro-pigments for intense, vibrant reds',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'majiblond',
        name: 'Majiblond Ultra',
        description: 'Up to 5 levels of lift with cool neutralization',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'smartbond',
        name: 'Smartbond',
        description: 'In-salon protective system for all color services',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'luocolor',
        name: 'LuoColor',
        description: 'Ammonia-free with luminous reflects and nutri-shine',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'majirel-cool-cover',
        name: 'Majirel Cool Cover',
        description: 'Cool brown shades with anti-red/orange technology',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'eugene-perma',
    name: 'Eugène Perma',
    country: 'France',
    description: 'French professional hair care',
    lines: [
      { id: 'carmen', name: 'Carmen', description: 'Professional color line' },
      { id: 'solaris', name: 'Solaris', description: 'Lightening products' },
      { id: 'artiste', name: 'Artiste', description: 'Creative color' },
    ],
  },
  {
    id: 'phyto',
    name: 'Phyto Professional',
    country: 'France',
    description: 'Botanical hair color with plant-based formulations',
    lines: [
      {
        id: 'phytocolor',
        name: 'Phytocolor Professional',
        description: 'Botanical permanent color with plant extracts',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'phytospecific',
        name: 'PhytoSpecific Professional',
        description: 'Specialized botanical color for textured hair',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'phytobaume',
        name: 'Phytobaume Color Protect',
        description: 'Color-protecting botanical treatment',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  // Italian Brands
  {
    id: 'alfaparf',
    name: 'Alfaparf Milano',
    country: 'Italy',
    description: 'Italian professional excellence in hair color and care',
    lines: [
      {
        id: 'evolution',
        name: 'Evolution of Color',
        description: 'Permanent hair color with advanced Italian technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'revolution',
        name: 'Revolution',
        description: 'Ammonia-free permanent color with gentle formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'yellow',
        name: 'Yellow',
        description: 'Fashion color line for creative looks',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Professional lightening system up to 8 levels',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'color-wear',
        name: 'Color Wear',
        description: 'Direct dye color for vibrant temporary results',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'precious-nature',
        name: 'Precious Nature',
        description: 'Natural ingredients color with organic extracts',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'semi-di-lino',
        name: 'Semi di Lino',
        description: 'Luxury treatment line with linseed oil',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'illuminating',
        name: 'Illuminating',
        description: 'Shine-enhancing color treatment system',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'inebrya',
    name: 'Inebrya',
    country: 'Italy',
    description: 'Italian innovation in hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      { id: 'bionic', name: 'Bionic', description: 'Ammonia-free color' },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Lightening products',
      },
      {
        id: 'color-perfect',
        name: 'Color Perfect',
        description: 'Professional color line',
      },
    ],
  },
  {
    id: 'framesi',
    name: 'Framesi',
    country: 'Italy',
    description: 'Italian professional hair color excellence',
    lines: [
      {
        id: 'framcolor',
        name: 'FramColor',
        description: 'Permanent hair color with Italian technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'eclectic-care',
        name: 'Eclectic Care',
        description: 'Ammonia-free permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'decolor-b',
        name: 'Decolor B',
        description: 'Professional lightening powder',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'hair-treatment',
        name: 'Hair Treatment',
        description: 'Color care and protection system',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'davines',
    name: 'Davines',
    country: 'Italy',
    description: 'Sustainable Italian hair color',
    lines: [
      { id: 'mask', name: 'Mask', description: 'Conditioning direct color' },
      {
        id: 'finest-pigments',
        name: 'Finest Pigments',
        description: 'Semi-permanent color',
      },
      {
        id: 'a-new-colour',
        name: 'A New Colour',
        description: 'Permanent color system',
      },
    ],
  },
  {
    id: 'kemon',
    name: 'Kemon',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      { id: 'kroma', name: 'Kroma', description: 'Permanent color' },
      { id: 'nayo', name: 'Nayo', description: 'Ammonia-free color' },
      { id: 'lunex', name: 'Lunex', description: 'Lightening system' },
    ],
  },
  {
    id: 'selective',
    name: 'Selective Professional',
    country: 'Italy',
    description: 'Italian color innovation',
    lines: [
      { id: 'colorevo', name: 'ColorEvo', description: 'Permanent hair color' },
      { id: 'reverso', name: 'Reverso', description: 'Hair color remover' },
      { id: 'decolor', name: 'Decolor', description: 'Lightening powder' },
    ],
  },

  // Spanish Brands
  {
    id: 'salerm',
    name: 'Salerm Cosmetics',
    country: 'Spain',
    description: 'Spanish professional hair care and color innovation',
    lines: [
      {
        id: 'vison',
        name: 'Vison',
        description: 'Permanent hair color with superior coverage',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'biokera-natura-color',
        name: 'Biokera Natura Color',
        description: 'Oxidation color formulated with natural oils and active ingredients',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'zero',
        name: 'Zero',
        description: 'Professional coloration with maximum fiber care and luminous tones',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'hi-repair',
        name: 'Hi Repair',
        description: 'Reconstructive treatment system',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'technique',
        name: 'Technique',
        description: 'Professional styling and finishing products',
        category: 'styling',
        isColorLine: false,
      },
      {
        id: 'biokera-natura',
        name: 'Biokera Natura',
        description: 'Natural active ingredients treatment line',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'color-reverse',
        name: 'Color Reverse',
        description: 'Professional color remover system',
        category: 'other',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'arkhe',
    name: 'Arkhé Cosmetics',
    country: 'Spain',
    description: 'Premium Spanish brand revolutionizing professional hair color',
    lines: [
      {
        id: 'color-pure',
        name: 'Color Pure',
        description: 'Revolutionary professional hair coloration system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'vibrant-well-aging',
        name: 'Vibrant Well-Aging Booster',
        description: 'Pro-age hair treatment for mature hair',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'lendan',
    name: 'Lendan Cosmetics',
    country: 'Spain',
    description: 'Spanish innovation in professional hair color',
    lines: [
      {
        id: 'lendan-color',
        name: 'Lendan Color',
        description: 'Professional permanent color with advanced technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'no-ammonia',
        name: 'No Ammonia',
        description: 'Ammonia-free permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'semi-permanent',
        name: 'Semi Permanent',
        description: 'Gentle semi-permanent color option',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bleaching',
        name: 'Bleaching',
        description: 'Professional lightening products up to 8 levels',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'activator',
        name: 'Activator',
        description: 'Professional developer range 10-40 vol',
        category: 'developer',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'tahe',
    name: 'Tahe Professional',
    country: 'Spain',
    description: 'Spanish professional hair care and color',
    lines: [
      {
        id: 'organic-care',
        name: 'Organic Care Color',
        description: 'Natural organic professional hair color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lumiere',
        name: 'Lumiere',
        description: 'Professional lightening system up to 7 levels',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'magic',
        name: 'Magic Color',
        description: 'Professional permanent color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'botanic',
        name: 'Botanic Tricology',
        description: 'Botanical hair treatment and care line',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  // American Brands
  {
    id: 'j-beverly-hills',
    name: 'J Beverly Hills',
    country: 'USA',
    description: 'Luxury professional hair color with pharmaceutical-grade pigments',
    lines: [
      {
        id: 'colour-line',
        name: 'Colour Line',
        description: 'Coloración permanente con pigmentos farmacéuticos de alta pureza (99,98%)',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'fashion-colour',
        name: 'Fashion Colour',
        description: 'Coloración semipermanente/directa de alta intensidad para colores vibrantes',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'liquid-luminosity',
        name: 'Liquid Luminosity',
        description: 'Brillos líquidos tipo gloss sin amoníaco para matizar y avivar el color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'platinum',
        name: 'Platinum',
        description: 'Sistema de aclarado profesional para tonos súper aclarantes',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'clear',
        name: 'Clear',
        description: 'Diluyente y mezclador para formulaciones personalizadas',
        category: 'other',
        isColorLine: true,
      },
      {
        id: 'blue',
        name: 'Blue',
        description: 'Línea de cuidado y tratamiento profesional',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'matrix',
    name: 'Matrix',
    country: 'USA',
    description: 'Professional color innovation',
    lines: [
      {
        id: 'socolor',
        name: 'SoColor',
        description: 'Permanent creme hair color',
      },
      {
        id: 'colorsync',
        name: 'ColorSync',
        description: 'No-lift deposit-only color',
      },
      {
        id: 'light-master',
        name: 'Light Master',
        description: 'Lightening system',
      },
      {
        id: 'color-insider',
        name: 'Color Insider',
        description: 'Fashion-forward shades',
      },
      {
        id: 'socolor-cult',
        name: 'SoColor Cult',
        description: 'Direct dye color',
      },
      {
        id: 'socolor-beauty',
        name: 'SoColor Beauty',
        description: 'Ammonia-free color',
      },
    ],
  },
  {
    id: 'redken',
    name: 'Redken',
    country: 'USA',
    description: 'Science-based hair color',
    lines: [
      {
        id: 'shades-eq',
        name: 'Shades EQ',
        description: 'Acidic conditioning color',
      },
      {
        id: 'chromatics',
        name: 'Chromatics',
        description: 'Ultra-rich fashion color',
      },
      {
        id: 'color-extend',
        name: 'Color Extend',
        description: 'Color-protecting care',
      },
      {
        id: 'flash-lift',
        name: 'Flash Lift',
        description: 'Lightening powder',
      },
      {
        id: 'cover-fusion',
        name: 'Cover Fusion',
        description: 'Gray coverage',
      },
    ],
  },
  {
    id: 'joico',
    name: 'Joico',
    country: 'USA',
    description: 'Quadramine complex technology for hair strength and color',
    lines: [
      {
        id: 'lumishine',
        name: 'LumiShine',
        description: 'Demi-permanent liquid color with ArgiPlex technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'vero-k-pak',
        name: 'Vero K-PAK',
        description: 'Permanent creme color with Quadramine complex',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'blonde-life',
        name: 'Blonde Life',
        description: 'Lightening system with bond protection',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'color-intensity',
        name: 'Color Intensity',
        description: 'Semi-permanent color for vibrant results',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'instamat',
        name: 'InstaMat',
        description: 'Toning system for perfect color correction',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'k-pak-color-therapy',
        name: 'K-PAK Color Therapy',
        description: 'Color protection and repair system',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'defy-damage',
        name: 'Defy Damage',
        description: 'Protective system for color-treated hair',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'aveda',
    name: 'Aveda',
    country: 'USA',
    description: 'Plant-based professional color with 99% naturally derived ingredients',
    lines: [
      {
        id: 'full-spectrum',
        name: 'Full Spectrum',
        description: 'Permanent hair color with plant-based ingredients',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'enlightener',
        name: 'Enlightener',
        description: 'Lightening powder with plant-based conditioning',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'demi-plus',
        name: 'Demi+',
        description: 'Demi-permanent color with botanical extracts',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'color-conserve',
        name: 'Color Conserve',
        description: 'Color protection system with plant extracts',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'botanical-repair',
        name: 'Botanical Repair',
        description: 'Strengthening treatment for color-damaged hair',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'nutriplenish',
        name: 'Nutriplenish',
        description: 'Deep nourishment for color-treated hair',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'paul-mitchell',
    name: 'Paul Mitchell',
    country: 'USA',
    description: 'Cruelty-free professional color and styling',
    lines: [
      {
        id: 'the-color',
        name: 'The Color',
        description: 'Permanent hair color with Inkworks technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'shines',
        name: 'Shines',
        description: 'Demi-permanent color for glossy results',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lighten-up',
        name: 'Lighten Up',
        description: 'Lightening products for professional use',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'inkworks',
        name: 'Inkworks',
        description: 'Gray coverage color with superior coverage',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'pop-xg',
        name: 'Pop XG',
        description: 'Fashion color for creative looks',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'forever-blonde',
        name: 'Forever Blonde',
        description: 'Specialized care for blonde hair',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'color-protect',
        name: 'Color Protect',
        description: 'Color protection and preservation system',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'ultimate-color-repair',
        name: 'Ultimate Color Repair',
        description: 'Intensive repair for color-damaged hair',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  {
    id: 'pravana',
    name: 'Pravana',
    country: 'USA',
    description: 'Vivid fashion color specialists',
    lines: [
      {
        id: 'chromasilk',
        name: 'ChromaSilk',
        description: 'Vivid fashion colors',
      },
      {
        id: 'pure-light',
        name: 'Pure Light',
        description: 'Lightening system',
      },
      {
        id: 'express-tones',
        name: 'Express Tones',
        description: 'Quick toning colors',
      },
      { id: 'vivids', name: 'Vivids', description: 'Direct dye colors' },
    ],
  },
  {
    id: 'revlon',
    name: 'Revlon Professional',
    country: 'USA',
    description: 'Professional color expertise',
    lines: [
      {
        id: 'revlonissimo',
        name: 'Revlonissimo',
        description: 'High-performance color',
      },
      {
        id: 'young-color-excel',
        name: 'Young Color Excel',
        description: 'Ammonia-free color',
      },
      {
        id: 'nutri-color',
        name: 'Nutri Color',
        description: 'Conditioning color',
      },
      {
        id: 'colorsmetique',
        name: 'Colorsmetique',
        description: 'Permanent color',
      },
    ],
  },
  {
    id: 'clairol',
    name: 'Clairol Professional',
    country: 'USA',
    description: 'Professional color innovation',
    lines: [
      {
        id: 'premium-creme',
        name: 'Premium Creme',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'jazzing',
        name: 'Jazzing',
        description: 'Temporary hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bw2',
        name: 'BW2',
        description: 'Lightening powder',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'soy4plex',
        name: 'Soy4Plex',
        description: 'Conditioning color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'kenra',
    name: 'Kenra Professional',
    country: 'USA',
    description: 'Professional hair color',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'demi',
        name: 'Demi',
        description: 'Demi-permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lightener',
        name: 'Lightener',
        description: 'Bleaching products',
        category: 'bleaching',
        isColorLine: true,
      },
    ],
  },



  // Japanese Brands
  {
    id: 'milbon',
    name: 'Milbon',
    country: 'Japan',
    description: 'Japanese hair color technology',
    lines: [
      { id: 'ordeve', name: 'Ordeve', description: 'Permanent hair color' },
      { id: 'addicthy', name: 'Addicthy', description: 'Fashion color' },
      { id: 'deesses', name: 'Deesses', description: 'Hair care line' },
    ],
  },
  {
    id: 'lebel',
    name: 'Lebel Cosmetics',
    country: 'Japan',
    description: 'Japanese professional hair care',
    lines: [
      { id: 'materia', name: 'Materia', description: 'Hair color line' },
      { id: 'luquias', name: 'Luquias', description: 'Premium color' },
      { id: 'theo', name: 'Theo', description: 'Scalp care color' },
    ],
  },
  {
    id: 'shiseido',
    name: 'Shiseido Professional',
    country: 'Japan',
    description: 'Japanese beauty innovation',
    lines: [
      { id: 'primience', name: 'Primience', description: 'Hair color system' },
      {
        id: 'crystallizing',
        name: 'Crystallizing',
        description: 'Straightening color',
      },
    ],
  },

  // Dutch Brands
  {
    id: 'keune',
    name: 'Keune',
    country: 'Netherlands',
    description: 'Dutch professional hair color',
    lines: [
      {
        id: 'tinta-color',
        name: 'Tinta Color',
        description: 'Permanent hair color',
      },
      {
        id: 'semi-color',
        name: 'Semi Color',
        description: 'Demi-permanent color',
      },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Lightening products',
      },
      { id: 'so-pure', name: 'So Pure', description: 'Natural color line' },
    ],
  },

  // British Brands
  {
    id: 'tigi',
    name: 'TIGI Professional',
    country: 'United Kingdom',
    description: 'British creative hair color',
    lines: [
      { id: 'creative', name: 'Creative', description: 'Fashion color line' },
      { id: 'colour', name: 'Colour', description: 'Permanent hair color' },
      { id: 'blonde', name: 'Blonde', description: 'Lightening system' },
    ],
  },
  {
    id: 'wella-uk',
    name: 'Wella UK',
    country: 'United Kingdom',
    description: 'British Wella division',
    lines: [
      {
        id: 'professionals',
        name: 'Professionals',
        description: 'UK professional line',
      },
    ],
  },

  // Australian Brands
  {
    id: 'kevin-murphy',
    name: 'Kevin Murphy',
    country: 'Australia',
    description: 'Australian luxury hair color',
    lines: [
      { id: 'color-me', name: 'Color.Me', description: 'Fashion color line' },
      {
        id: 'blonde-angel',
        name: 'Blonde.Angel',
        description: 'Lightening treatment',
      },
    ],
  },

  // Canadian Brands
  {
    id: 'schwarzkopf-canada',
    name: 'Schwarzkopf Canada',
    country: 'Canada',
    description: 'Canadian professional division',
    lines: [{ id: 'igora-ca', name: 'Igora CA', description: 'Canadian color line' }],
  },

  // Brazilian Brands
  {
    id: 'amend',
    name: 'Amend',
    country: 'Brazil',
    description: 'Brazilian professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'expertise', name: 'Expertise', description: 'Premium color' },
    ],
  },
  {
    id: 'felps',
    name: 'Felps Professional',
    country: 'Brazil',
    description: 'Brazilian hair color innovation',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      { id: 'omega', name: 'Omega', description: 'Hair treatment color' },
    ],
  },

  // Korean Brands
  {
    id: 'mise-en-scene',
    name: 'Mise En Scene',
    country: 'South Korea',
    description: 'Korean professional hair color',
    lines: [
      {
        id: 'hello-bubble',
        name: 'Hello Bubble',
        description: 'Foam hair color',
      },
      { id: 'perfect', name: 'Perfect', description: 'Professional color' },
    ],
  },

  // Russian Brands
  {
    id: 'estel',
    name: 'Estel Professional',
    country: 'Russia',
    description: 'Russian professional hair color',
    lines: [
      { id: 'de-luxe', name: 'De Luxe', description: 'Premium color line' },
      { id: 'essex', name: 'Essex', description: 'Professional color' },
      {
        id: 'princess',
        name: 'Princess Essex',
        description: 'Ammonia-free color',
      },
      {
        id: 'haute-couture',
        name: 'Haute Couture',
        description: 'Fashion color',
      },
    ],
  },
  {
    id: 'kapous',
    name: 'Kapous Professional',
    country: 'Russia',
    description: 'Russian hair color brand',
    lines: [
      {
        id: 'hyaluronic',
        name: 'Hyaluronic',
        description: 'Hyaluronic acid color',
      },
      {
        id: 'magic-keratin',
        name: 'Magic Keratin',
        description: 'Keratin color',
      },
      {
        id: 'non-ammonia',
        name: 'Non Ammonia',
        description: 'Ammonia-free color',
      },
    ],
  },

  // Polish Brands
  {
    id: 'indola',
    name: 'Indola',
    country: 'Poland',
    description: 'Polish professional hair color',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Caring Color',
        description: 'Caring permanent color',
      },
      { id: 'rapid', name: 'Rapid Blond', description: 'Fast lightening' },
      {
        id: 'profession',
        name: 'Profession',
        description: 'Professional color line',
      },
    ],
  },

  // Czech Brands
  {
    id: 'subrina',
    name: 'Subrina Professional',
    country: 'Czech Republic',
    description: 'Czech professional hair color',
    lines: [
      { id: 'unique', name: 'Unique', description: 'Professional color line' },
      { id: 'mad-touch', name: 'Mad Touch', description: 'Fashion color' },
    ],
  },

  // Swedish Brands
  {
    id: 'maria-nila',
    name: 'Maria Nila',
    country: 'Sweden',
    description: 'Swedish sustainable hair color',
    lines: [
      {
        id: 'colour-refresh',
        name: 'Colour Refresh',
        description: 'Color depositing mask',
      },
      { id: 'pure-color', name: 'Pure Color', description: 'Vegan hair color' },
    ],
  },

  // Norwegian Brands
  {
    id: 'cutrin',
    name: 'Cutrin',
    country: 'Norway',
    description: 'Nordic professional hair color',
    lines: [
      { id: 'aurora', name: 'Aurora', description: 'Permanent color' },
      {
        id: 'reflection',
        name: 'Reflection',
        description: 'Demi-permanent color',
      },
    ],
  },

  // Additional International Brands
  {
    id: 'fanola',
    name: 'Fanola Professional',
    country: 'Italy',
    description: 'Italian professional color innovation',
    lines: [
      {
        id: 'color',
        name: 'Fanola Color',
        description: 'Permanent professional hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'botugen',
        name: 'Botugen',
        description: 'Botox-effect permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'oro-therapy',
        name: 'Oro Therapy Color',
        description: 'Gold-infused permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'no-yellow',
        name: 'No Yellow Shampoo',
        description: 'Anti-yellow maintenance shampoo',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'lisap',
    name: 'Lisap Milano',
    country: 'Italy',
    description: 'Italian hair color innovation',
    lines: [
      { id: 'lisaplex', name: 'LisapLex', description: 'Bond builder system' },
      {
        id: 'easy-absolute',
        name: 'Easy Absolute',
        description: 'Ammonia-free color',
      },
      { id: 'splendor', name: 'Splendor', description: 'Permanent color' },
    ],
  },
  {
    id: 'bbcos',
    name: 'BBCos',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      {
        id: 'keratin',
        name: 'Keratin Color',
        description: 'Keratin-enriched color',
      },
      {
        id: 'innovation',
        name: 'Innovation Evo',
        description: 'Advanced color technology',
      },
    ],
  },
  {
    id: 'farmavita',
    name: 'Farmavita',
    country: 'Italy',
    description: 'Italian hair color expertise',
    lines: [
      {
        id: 'life-color',
        name: 'Life Color Plus',
        description: 'Permanent hair color',
      },
      { id: 'bleach', name: 'Bleach', description: 'Lightening powder' },
    ],
  },
  {
    id: 'vitality',
    name: "Vitality's",
    country: 'Italy',
    description: 'Italian natural hair color',
    lines: [
      {
        id: 'tone-intense',
        name: 'Tone Intense',
        description: 'Intensive color',
      },
      { id: 'art', name: 'Art', description: 'Creative color line' },
    ],
  },
  {
    id: 'echosline',
    name: 'Echosline',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'fashion', name: 'Fashion Color', description: 'Trend colors' },
    ],
  },
  {
    id: 'green-light',
    name: 'Green Light',
    country: 'Italy',
    description: 'Italian eco-friendly hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Eco-friendly color' },
      { id: 'keratin', name: 'Keratin', description: 'Keratin color line' },
    ],
  },
  {
    id: 'hair-company',
    name: 'Hair Company',
    country: 'Italy',
    description: 'Italian hair color innovation',
    lines: [
      {
        id: 'inimitable',
        name: 'Inimitable',
        description: 'Premium color line',
      },
      {
        id: 'professional',
        name: 'Professional',
        description: 'Professional color',
      },
    ],
  },
  {
    id: 'oyster',
    name: 'Oyster Cosmetics',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      {
        id: 'perlacolor',
        name: 'Perlacolor',
        description: 'Pearl-enriched color',
      },
      {
        id: 'perlaplus',
        name: 'Perlaplus',
        description: 'Advanced color system',
      },
    ],
  },
  {
    id: 'dikson',
    name: 'Dikson',
    country: 'Italy',
    description: 'Italian hair color tradition',
    lines: [
      { id: 'color', name: 'Color', description: 'Traditional color line' },
      { id: 'drop-color', name: 'Drop Color', description: 'Liquid color' },
    ],
  },
  {
    id: 'bionike',
    name: 'BioNike',
    country: 'Italy',
    description: 'Italian dermatological hair color',
    lines: [
      { id: 'shine-on', name: 'Shine On', description: 'Gentle hair color' },
      { id: 'defence', name: 'Defence', description: 'Sensitive scalp color' },
    ],
  },
  {
    id: 'cotril',
    name: 'Cotril',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'creative', name: 'Creative Walk', description: 'Fashion color' },
    ],
  },
  {
    id: 'maxima',
    name: 'Maxima',
    country: 'Italy',
    description: 'Italian hair color solutions',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'bleach', name: 'Bleach', description: 'Lightening system' },
    ],
  },
  {
    id: 'nouvelle',
    name: 'Nouvelle',
    country: 'Italy',
    description: 'Italian color innovation',
    lines: [
      {
        id: 'hair-color',
        name: 'Hair Color',
        description: 'Professional color line',
      },
      { id: 'touch', name: 'Touch', description: 'Quick color touch-up' },
    ],
  },
  {
    id: 'periche',
    name: 'Periche Professional',
    country: 'Spain',
    description: 'Spanish professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Permanent hair color' },
      {
        id: 'cybercolor',
        name: 'Cybercolor',
        description: 'Advanced color technology',
      },
    ],
  },
  {
    id: 'montibello',
    name: 'Montibello',
    country: 'Spain',
    description: 'Spanish hair color expertise',
    lines: [
      { id: 'cromatone', name: 'Cromatone', description: 'Professional color' },
      { id: 'oalia', name: 'Oalia', description: 'Ammonia-free color' },
    ],
  },
  {
    id: 'kativa',
    name: 'Kativa',
    country: 'Spain',
    description: 'Spanish natural hair care',
    lines: [
      {
        id: 'keratin',
        name: 'Keratin',
        description: 'Keratin color treatment',
      },
      { id: 'collagen', name: 'Collagen', description: 'Anti-aging color' },
    ],
  },
  {
    id: 'exitenn',
    name: 'Exitenn',
    country: 'Spain',
    description: 'Spanish professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      { id: 'fashion', name: 'Fashion Color', description: 'Trend colors' },
    ],
  },
  {
    id: 'nirvel',
    name: 'Nirvel Professional',
    country: 'Spain',
    description: 'Spanish hair color innovation',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'artx', name: 'ArtX', description: 'Creative color line' },
    ],
  },
  {
    id: 'postquam',
    name: 'Postquam Professional',
    country: 'Spain',
    description: 'Spanish professional hair care',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color line' },
      {
        id: 'keratin',
        name: 'Keratin',
        description: 'Keratin color treatment',
      },
    ],
  },
  {
    id: 'eugene-color',
    name: 'Eugène Color',
    country: 'France',
    description: 'French color expertise',
    lines: [
      {
        id: 'professional',
        name: 'Professional',
        description: 'Professional color line',
      },
      { id: 'fashion', name: 'Fashion', description: 'Fashion color' },
    ],
  },
  {
    id: 'subtil',
    name: 'Subtil',
    country: 'France',
    description: 'French professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'blonde', name: 'Blonde', description: 'Lightening system' },
    ],
  },
  {
    id: 'ducastel',
    name: 'Ducastel Subtil',
    country: 'France',
    description: 'French hair color tradition',
    lines: [
      { id: 'subtil', name: 'Subtil', description: 'Traditional color line' },
      { id: 'design', name: 'Design', description: 'Creative color' },
    ],
  },

  {
    id: 'schwarzkopf-igora',
    name: 'Schwarzkopf Igora',
    country: 'Germany',
    description: 'German Igora specialist line',
    lines: [
      {
        id: 'royal-absolutes',
        name: 'Royal Absolutes',
        description: 'Mature hair color',
      },
      {
        id: 'royal-disheveled',
        name: 'Royal Disheveled',
        description: 'Fashion color',
      },
    ],
  },
  {
    id: 'lanza',
    name: 'Lanza',
    country: 'USA',
    description: 'American healing hair color',
    lines: [
      { id: 'healing', name: 'Healing', description: 'Healing color system' },
      {
        id: 'trauma',
        name: 'Trauma Treatment',
        description: 'Reconstructive color',
      },
    ],
  },
  {
    id: 'rusk',
    name: 'Rusk',
    country: 'USA',
    description: 'American professional hair color',
    lines: [
      {
        id: 'deepshine',
        name: 'Deepshine',
        description: 'Color-enhancing system',
      },
      { id: 'color', name: 'Color', description: 'Professional color line' },
    ],
  },
  {
    id: 'chi',
    name: 'CHI',
    country: 'USA',
    description: 'American ionic hair color',
    lines: [
      { id: 'ionic', name: 'Ionic', description: 'Ionic color system' },
      { id: 'color', name: 'Color', description: 'Professional color' },
    ],
  },
  {
    id: 'sebastian',
    name: 'Sebastian Professional',
    country: 'USA',
    description: 'American creative hair color',
    lines: [
      {
        id: 'cellophanes',
        name: 'Cellophanes',
        description: 'Shine color treatment',
      },
      { id: 'color', name: 'Color', description: 'Professional color line' },
    ],
  },
  {
    id: 'tigi-bed-head',
    name: 'TIGI Bed Head',
    country: 'United Kingdom',
    description: 'British creative color',
    lines: [
      { id: 'colour', name: 'Colour', description: 'Creative color line' },
      {
        id: 'dumb-blonde',
        name: 'Dumb Blonde',
        description: 'Blonde care system',
      },
    ],
  },
  {
    id: 'osmo',
    name: 'Osmo',
    country: 'United Kingdom',
    description: 'British professional hair color',
    lines: [
      { id: 'color', name: 'Color', description: 'Professional color' },
      { id: 'ikon', name: 'Ikon', description: 'Fashion color line' },
    ],
  },

  // Additional American Brands
  {
    id: 'ion',
    name: 'Ion',
    country: 'USA',
    description: 'Professional vibrant colors at Sally Beauty',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Brights',
        description: 'Vibrant permanent hair color',
      },
      {
        id: 'demi',
        name: 'Demi Permanent',
        description: 'Long-lasting demi-permanent color',
      },
      {
        id: 'brilliance',
        name: 'Color Brilliance',
        description: 'Brilliant shine formula',
      },
      {
        id: 'intensive',
        name: 'Intensive Shine',
        description: 'High-gloss color system',
      },
      {
        id: 'liquid',
        name: 'Liquid Hair Color',
        description: 'Easy-application liquid formula',
      },
    ],
  },
  {
    id: 'arctic-fox',
    name: 'Arctic Fox',
    country: 'USA',
    description: 'Semi-permanent vegan hair color',
    lines: [
      {
        id: 'semi-permanent',
        name: 'Semi-Permanent',
        description: 'Vegan & cruelty-free colors',
      },
      {
        id: 'diluter',
        name: 'Diluter',
        description: 'Mix to create pastel shades',
      },
      {
        id: 'virgin-pink',
        name: 'Virgin Pink Collection',
        description: 'Pink tone variations',
      },
      {
        id: 'aquamarine',
        name: 'Aquamarine Collection',
        description: 'Blue-green shades',
      },
    ],
  },
  {
    id: 'madison-reed',
    name: 'Madison Reed',
    country: 'USA',
    description: 'Professional-grade ammonia-free color',
    lines: [
      {
        id: 'radiant',
        name: 'Radiant Hair Color',
        description: 'Ammonia-free permanent color',
      },
      {
        id: 'root-touch-up',
        name: 'Root Touch Up',
        description: 'Quick root coverage',
      },
      {
        id: 'color-reviving',
        name: 'Color Reviving Gloss',
        description: 'Semi-permanent gloss treatment',
      },
    ],
  },
  {
    id: 'igk',
    name: 'IGK',
    country: 'USA',
    description: 'Modern professional hair color',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Color Kit',
        description: 'Salon-quality permanent color',
      },
      { id: 'foamo', name: 'Foamo', description: 'Foam-based color system' },
      {
        id: 'direct-dye',
        name: 'Direct Dye',
        description: 'Vibrant temporary colors',
      },
    ],
  },

  // Additional Brazilian Brands
  {
    id: 'cadiveu',
    name: 'Cadiveu Professional',
    country: 'Brazil',
    description: 'Brazilian professional hair solutions',
    lines: [
      {
        id: 'buriti',
        name: 'Buriti Mechas',
        description: 'Highlighting system with buriti oil',
      },
      {
        id: 'superclear',
        name: 'Superclear',
        description: 'High-lift lightening powder',
      },
      {
        id: 'color',
        name: 'Professional Color',
        description: 'Permanent color line',
      },
    ],
  },
  {
    id: 'truss',
    name: 'Truss Professional',
    country: 'Brazil',
    description: 'High-performance Brazilian hair color',
    lines: [
      {
        id: 'color',
        name: 'Professional Color',
        description: 'Advanced color technology',
      },
      {
        id: 'blond',
        name: 'Specific Blond',
        description: 'Specialized blonde treatments',
      },
      {
        id: 'nano',
        name: 'Nano Regeneration',
        description: 'Color with regenerative treatment',
      },
    ],
  },

  // Mexican Brands
  {
    id: 'recamier',
    name: 'Recamier Professional',
    country: 'Mexico',
    description: 'Mexican professional hair care',
    lines: [
      {
        id: 'saloon-in',
        name: 'SaloonIn',
        description: 'Professional permanent color',
      },
      {
        id: 'keratina',
        name: 'Keratina Color',
        description: 'Keratin-infused color',
      },
      {
        id: 'argan',
        name: 'Argan Color',
        description: 'Argan oil enriched color',
      },
    ],
  },
  {
    id: 'issue',
    name: 'Issue Professional',
    country: 'Mexico',
    description: 'Latin American professional color',
    lines: [
      {
        id: 'colorissue',
        name: 'Colorissue',
        description: 'Permanent hair color',
      },
      { id: 'deco', name: 'Deco', description: 'Lightening products' },
      {
        id: 'fantasy',
        name: 'Fantasy Colors',
        description: 'Fashion color collection',
      },
    ],
  },

  // Argentinian Brands
  {
    id: 'fidelite',
    name: 'Fidelité',
    country: 'Argentina',
    description: 'Argentinian professional hair color',
    lines: [
      {
        id: 'coloracion',
        name: 'Coloración Permanente',
        description: 'Permanent color system',
      },
      {
        id: 'nutri-color',
        name: 'Nutri Color',
        description: 'Nourishing color treatment',
      },
      {
        id: 'blonde',
        name: 'Blonde Expert',
        description: 'Blonde specialist line',
      },
    ],
  },

  // Colombian Brands
  {
    id: 'revlon-colombia',
    name: 'Revlon Professional Colombia',
    country: 'Colombia',
    description: 'Colombian division of Revlon Professional',
    lines: [
      {
        id: 'revlonissimo',
        name: 'Revlonissimo Colombia',
        description: 'Adapted for Latin hair',
      },
      {
        id: 'young-color',
        name: 'Young Color',
        description: 'Ammonia-free option',
      },
    ],
  },

  // Chilean Brands
  {
    id: 'saloon-in-chile',
    name: 'Saloon In',
    country: 'Chile',
    description: 'Chilean professional hair color',
    lines: [
      {
        id: 'color-cream',
        name: 'Color Cream',
        description: 'Creamy permanent color',
      },
      {
        id: 'lightening',
        name: 'Lightening System',
        description: 'Professional bleaching',
      },
      { id: 'toner', name: 'Toner Collection', description: 'Toning products' },
    ],
  },

  // Additional Asian Brands
  {
    id: 'napla',
    name: 'Napla',
    country: 'Japan',
    description: 'Japanese hair color innovation',
    lines: [
      {
        id: 'caretect',
        name: 'Caretect',
        description: 'Care-focused color system',
      },
      {
        id: 'n-color',
        name: 'N. Color',
        description: 'Natural color collection',
      },
      { id: 'bleach', name: 'Bleach Powder', description: 'Gentle lightening' },
    ],
  },
  {
    id: 'hoyu',
    name: 'Hoyu Professional',
    country: 'Japan',
    description: 'Japanese color technology leader',
    lines: [
      {
        id: 'promaster',
        name: 'Promaster Color Care',
        description: 'Professional color system',
      },
      { id: 'somarca', name: 'Somarca', description: 'Fashion color line' },
      {
        id: 'glamage',
        name: 'Glamage',
        description: 'Premium color collection',
      },
    ],
  },

  // African Brands
  {
    id: 'dark-and-lovely',
    name: 'Dark & Lovely Professional',
    country: 'South Africa',
    description: 'Professional products for textured hair',
    lines: [
      {
        id: 'fade-resist',
        name: 'Fade Resist',
        description: 'Long-lasting color for textured hair',
      },
      {
        id: 'go-intense',
        name: 'Go Intense',
        description: 'Ultra vibrant colors',
      },
      {
        id: 'precision',
        name: 'Precision Color',
        description: 'Precise color application',
      },
    ],
  },
  {
    id: 'ors',
    name: 'ORS Professional',
    country: 'South Africa',
    description: 'Olive oil-based professional color',
    lines: [
      {
        id: 'olive-oil',
        name: 'Olive Oil Color',
        description: 'Nourishing color system',
      },
      {
        id: 'professional',
        name: 'Professional Line',
        description: 'Salon-grade products',
      },
    ],
  },

  // Additional European Brands
  {
    id: 'alter-ego',
    name: 'Alter Ego Italy',
    country: 'Italy',
    description: 'Italian professional excellence',
    lines: [
      {
        id: 'technofruit',
        name: 'TechnoFruit Color',
        description: 'Fruit acid technology',
      },
      {
        id: 'blondego',
        name: 'BlondEgo',
        description: 'Blonde specialist range',
      },
      {
        id: 'color-ego',
        name: 'ColorEgo',
        description: 'Permanent color system',
      },
    ],
  },
  {
    id: 'be-hair',
    name: 'Be Hair',
    country: 'Italy',
    description: 'Italian sustainable hair color',
    lines: [
      {
        id: 'be-color',
        name: 'Be Color',
        description: 'Eco-friendly permanent color',
      },
      { id: '12-minute', name: '12 Minute', description: 'Fast-acting color' },
      { id: 'plex', name: 'Plex System', description: 'Bond-building color' },
    ],
  },

  // Turkish Brands
  {
    id: 'maxx-deluxe',
    name: 'Maxx Deluxe',
    country: 'Turkey',
    description: 'Turkish professional hair color',
    lines: [
      {
        id: 'premium',
        name: 'Premium Color',
        description: 'High-quality permanent color',
      },
      {
        id: 'lightening',
        name: 'Lightening Powder',
        description: 'Professional bleaching',
      },
      { id: 'toner', name: 'Toner Series', description: 'Toning collection' },
    ],
  },

  // Indian Brands
  {
    id: 'streax',
    name: 'Streax Professional',
    country: 'India',
    description: 'Indian professional hair color',
    lines: [
      {
        id: 'professional',
        name: 'Professional Color',
        description: 'Salon-grade color',
      },
      {
        id: 'insta-shine',
        name: 'Insta Shine',
        description: 'Instant shine color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'hair-serum',
        name: 'Color Serum',
        description: 'Serum-based color',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },





  {
    id: 'selective-professional',
    name: 'Selective Professional',
    country: 'Italy',
    description: 'Italian selective professional color',
    lines: [
      {
        id: 'colorevo',
        name: 'ColorEvo',
        description: 'Evolution permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'reverso',
        name: 'Reverso',
        description: 'Hair color remover system',
        category: 'other',
        isColorLine: true,
      },
    ],
  },

  // Spanish Professional Brands
  {
    id: 'lakme-professional',
    name: 'Lakme Professional',
    country: 'Spain',
    description: 'Spanish professional color innovation',
    lines: [
      {
        id: 'k-color',
        name: 'K.Color',
        description: 'Permanent professional color with keratin',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'teknia-color-stay',
        name: 'Teknia Color Stay',
        description: 'Color protection and maintenance system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'master',
        name: 'Master',
        description: 'Professional lightening system',
        category: 'bleaching',
        isColorLine: true,
      },
    ],
  },

  // Additional Premium International Brands
  {
    id: 'keune-professional',
    name: 'Keune Professional',
    country: 'Netherlands',
    description: 'Dutch premium professional hair color and care',
    lines: [
      {
        id: 'tinta-color',
        name: 'Tinta Color',
        description: 'Ammonia-free permanent color with silk protein',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'semi-color',
        name: 'Semi Color',
        description: 'Demi-permanent color for gentle coloring',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Professional lightening up to 7 levels',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'care-line',
        name: 'Care Line',
        description: 'Professional hair care and treatment',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  {
    id: 'davines-professional',
    name: 'Davines Professional',
    country: 'Italy',
    description: 'Italian sustainable professional hair color',
    lines: [
      {
        id: 'mask-vibrachrom',
        name: 'Mask Vibrachrom',
        description: 'Tone-on-tone color mask for vibrant results',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'alchemic',
        name: 'Alchemic',
        description: 'Color-enhancing shampoo and conditioner system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'oi',
        name: 'OI',
        description: 'Beauty treatment line with Roucou oil',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  {
    id: 'fanola-professional',
    name: 'Fanola Professional',
    country: 'Italy',
    description: 'Italian professional hair color, blonde specialist',
    lines: [
      {
        id: 'fanola-color',
        name: 'Fanola Color',
        description: 'Professional permanent color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'polvere-decolorante',
        name: 'Polvere Decolorante',
        description: 'Professional bleaching powder up to 8 levels',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'no-yellow-shampoo',
        name: 'No Yellow Shampoo',
        description: 'Anti-yellow toning system for blondes',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  {
    id: 'tec-italy-professional',
    name: 'TEC Italy Professional',
    country: 'Italy',
    description: 'Italian professional color innovation',
    lines: [
      {
        id: 'designer-color',
        name: 'Designer Color',
        description: 'Professional color with real tonalities and vibrant reflects',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lumina-lightener',
        name: 'Lumina Lightener',
        description: 'Professional lightening system',
        category: 'bleaching',
        isColorLine: true,
      },
    ],
  },
];

export const getLinesByBrandId = (brandId: string): ProductLine[] => {
  const brand = professionalHairColorBrands.find(b => b.id === brandId);
  return brand ? brand.lines : [];
};

export const getColorLinesByBrandId = (brandId: string): ProductLine[] => {
  const brand = professionalHairColorBrands.find(b => b.id === brandId);
  return brand ? brand.lines.filter(line => line.isColorLine === true) : [];
};

export const getBrandById = (brandId: string): Brand | undefined => {
  return professionalHairColorBrands.find(b => b.id === brandId);
};

export const searchBrands = (query: string): Brand[] => {
  if (!query.trim()) return professionalHairColorBrands;

  const lowercaseQuery = query.toLowerCase();
  return professionalHairColorBrands.filter(
    brand =>
      brand.name.toLowerCase().includes(lowercaseQuery) ||
      brand.country.toLowerCase().includes(lowercaseQuery) ||
      brand.lines.some(line => line.name.toLowerCase().includes(lowercaseQuery))
  );
};

/**
 * Valida que todas las líneas tengan la estructura correcta
 */
export const validateBrandLines = (): { valid: boolean; issues: string[] } => {
  const issues: string[] = [];

  professionalHairColorBrands.forEach(brand => {
    brand.lines.forEach(line => {
      if (!line.category) {
        issues.push(`${brand.name} - ${line.name}: Falta categoría`);
      }
      if (line.isColorLine === undefined) {
        issues.push(`${brand.name} - ${line.name}: Falta isColorLine`);
      }
    });
  });

  return {
    valid: issues.length === 0,
    issues,
  };
};

/**
 * Obtiene estadísticas de líneas por categoría
 */
export const getBrandLinesStats = () => {
  const stats = {
    total: 0,
    colorLines: 0,
    treatmentLines: 0,
    stylingLines: 0,
    bleachingLines: 0,
    developerLines: 0,
    otherLines: 0,
    brandsWithoutColorLines: 0,
    brandsWithColorLines: 0,
  };

  professionalHairColorBrands.forEach(brand => {
    const brandColorLines = brand.lines.filter(line => line.isColorLine === true);
    if (brandColorLines.length > 0) {
      stats.brandsWithColorLines++;
    } else {
      stats.brandsWithoutColorLines++;
    }

    brand.lines.forEach(line => {
      stats.total++;
      if (line.isColorLine) {
        stats.colorLines++;
      }
      switch (line.category) {
        case 'treatment':
          stats.treatmentLines++;
          break;
        case 'styling':
          stats.stylingLines++;
          break;
        case 'bleaching':
          stats.bleachingLines++;
          break;
        case 'developer':
          stats.developerLines++;
          break;
        case 'other':
          stats.otherLines++;
          break;
      }
    });
  });

  return stats;
};

/**
 * Obtiene marcas ordenadas por popularidad/importancia internacional
 */
export const getBrandsByPopularity = (): Brand[] => {
  // Orden de popularidad basado en presencia internacional y uso profesional
  const popularityOrder = [
    // Tier 1: Marcas globales premium
    'wella',
    'loreal',
    'schwarzkopf',
    'redken',
    'matrix',
    'goldwell',

    // Tier 2: Marcas internacionales reconocidas
    'alfaparf',
    'joico',
    'pravana',
    'revlon',
    'indola',
    'keune-professional',

    // Tier 3: Marcas españolas y europeas
    'salerm',
    'arkhe',
    'lendan',
    'tahe',
    'davines-professional',
    'inebrya',
    'fanola-professional',
    'tec-italy-professional',

    // Tier 4: Marcas americanas especializadas
    'paul-mitchell',
    'kenra',
    'aveda',
    'sebastian-professional',

    // Tier 5: Marcas asiáticas y emergentes
    'milbon',
    'shiseido',
    'napla',
    'hoyu',

    // Tier 6: Marcas regionales importantes
    'lowell',
    'felps',
    'kativa',
    'cadiveu',
    'truss',
  ];

  const orderedBrands: Brand[] = [];
  const remainingBrands = [...professionalHairColorBrands];

  // Agregar marcas en orden de popularidad
  popularityOrder.forEach(brandId => {
    const brandIndex = remainingBrands.findIndex(b => b.id === brandId);
    if (brandIndex !== -1) {
      orderedBrands.push(remainingBrands[brandIndex]);
      remainingBrands.splice(brandIndex, 1);
    }
  });

  // Agregar marcas restantes al final
  orderedBrands.push(...remainingBrands);

  return orderedBrands;
};

/**
 * Obtiene marcas recomendadas por región
 */
export const getRecommendedBrandsByRegion = (region: string): Brand[] => {
  const regionalRecommendations: Record<string, string[]> = {
    Europe: [
      'wella',
      'loreal',
      'schwarzkopf',
      'goldwell',
      'salerm',
      'arkhe',
      'davines-professional',
      'keune-professional',
    ],
    'North America': ['redken', 'matrix', 'paul-mitchell', 'kenra', 'joico', 'pravana', 'aveda'],
    'South America': ['loreal', 'wella', 'alfaparf', 'lowell', 'felps', 'kativa', 'cadiveu'],
    Asia: ['milbon', 'shiseido', 'napla', 'hoyu', 'wella', 'loreal', 'schwarzkopf'],
    Spain: ['salerm', 'arkhe', 'lendan', 'tahe', 'wella', 'loreal', 'schwarzkopf'],
    Italy: [
      'alfaparf',
      'davines-professional',
      'inebrya',
      'fanola-professional',
      'tec-italy-professional',
    ],
    Germany: ['wella', 'schwarzkopf', 'goldwell', 'kadus'],
    France: ['loreal', 'eugene-perma', 'phyto', 'subtil'],
    Netherlands: ['keune-professional', 'wella', 'loreal'],
    USA: ['redken', 'matrix', 'paul-mitchell', 'joico', 'pravana', 'kenra', 'aveda'],
    Brazil: ['lowell', 'felps', 'cadiveu', 'truss', 'amend'],
    Japan: ['milbon', 'shiseido', 'napla', 'hoyu', 'lebel'],
  };

  const recommendedIds = regionalRecommendations[region] || [];
  return recommendedIds
    .map(id => professionalHairColorBrands.find(b => b.id === id))
    .filter(Boolean) as Brand[];
};

export const getBrandsByCountry = (country: string): Brand[] => {
  return professionalHairColorBrands.filter(brand => brand.country === country);
};

export const getAllCountries = (): string[] => {
  const countrySet = new Set(professionalHairColorBrands.map(brand => brand.country));
  const countries: string[] = [];
  countrySet.forEach(country => countries.push(country));
  return countries.sort();
};

/**
 * Información técnica por marca
 */
export interface BrandTechnicalInfo {
  numberingSystem: string;
  maxDeveloperVolume: number;
  specialFeatures: string[];
  compatibleBrands?: string[];
}

export const getBrandTechnicalInfo = (brandId: string): BrandTechnicalInfo | null => {
  const technicalData: Record<string, BrandTechnicalInfo> = {
    wella: {
      numberingSystem: 'International system with / (e.g., 8/38)',
      maxDeveloperVolume: 40,
      specialFeatures: ['ME+ technology', 'Microlight technology', 'Anti-yellow molecules'],
      compatibleBrands: ['schwarzkopf', 'goldwell'],
    },
    loreal: {
      numberingSystem: 'Point system with . (e.g., 8.3)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Ionène G + Incell', 'Oil Delivery System', 'Carmilane micro-pigments'],
      compatibleBrands: ['redken', 'matrix'],
    },
    schwarzkopf: {
      numberingSystem: 'Dash system with - (e.g., 8-4)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Fibreplex technology', 'BlondMe bonding', 'Royal Absolutes'],
      compatibleBrands: ['wella', 'goldwell'],
    },
    salerm: {
      numberingSystem: 'Traditional European system',
      maxDeveloperVolume: 40,
      specialFeatures: ['Biokera natural oils', 'Zero ammonia-free', 'Vison coverage'],
      compatibleBrands: ['wella', 'loreal'],
    },
    arkhe: {
      numberingSystem: 'Modern European system',
      maxDeveloperVolume: 40,
      specialFeatures: [
        'Color Pure technology',
        'Well-aging formulas',
        'Premium Spanish innovation',
      ],
      compatibleBrands: ['salerm', 'wella'],
    },
    redken: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Acidic pH technology', 'Chromatics ultra-rich', 'Science-based formulas'],
      compatibleBrands: ['matrix', 'loreal'],
    },
    matrix: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['SoColor technology', 'ColorSync deposit-only', 'Light Master system'],
      compatibleBrands: ['redken', 'loreal'],
    },
  };

  return technicalData[brandId] || null;
};

/**
 * Obtiene marcas compatibles para conversiones
 */
export const getCompatibleBrands = (brandId: string): Brand[] => {
  const techInfo = getBrandTechnicalInfo(brandId);
  if (!techInfo?.compatibleBrands) return [];

  return techInfo.compatibleBrands
    .map(id => professionalHairColorBrands.find(b => b.id === id))
    .filter(Boolean) as Brand[];
};
